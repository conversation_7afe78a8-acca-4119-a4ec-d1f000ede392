<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索条件标签功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        .test-url {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .test-steps {
            list-style: none;
            padding: 0;
        }
        .test-steps li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .test-steps li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        .expected-result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 搜索条件标签功能测试指南</h1>
        
        <div class="success">
            <strong>✅ 修复完成！</strong> 已修复 "Cannot access 'metadata' before initialization" 错误，现在可以正常测试搜索条件标签功能。
        </div>

        <div class="test-section">
            <div class="test-title">1. 基础页面加载测试</div>
            <div class="test-description">
                验证页面能够正常加载，不再出现初始化错误。
            </div>
            <div class="test-url">http://localhost:3000/data/list/us_pmn</div>
            <ul class="test-steps">
                <li>页面正常加载，无 JavaScript 错误</li>
                <li>筛选器面板正常显示</li>
                <li>数据表格正常显示</li>
                <li>搜索条件标签区域准备就绪（初始为空）</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">2. 简单筛选条件标签测试</div>
            <div class="test-description">
                测试左侧筛选器面板的条件是否能正确显示为标签。
            </div>
            <ul class="test-steps">
                <li>在左侧筛选器中选择任意条件（如产品类别）</li>
                <li>点击"搜索"按钮</li>
                <li>观察表格上方是否出现搜索条件标签</li>
                <li>标签格式应为："字段名: 值"</li>
                <li>标签右侧应有 ❌ 删除按钮</li>
            </ul>
            <div class="expected-result">
                <strong>预期结果：</strong> 表格上方显示类似 "产品类别: 第一类 ❌" 的标签
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">3. 全局搜索标签测试</div>
            <div class="test-description">
                测试全局搜索（allFields）是否能正确显示为标签。
            </div>
            <div class="test-url">http://localhost:3000/data/list/us_pmn?allFields=导管</div>
            <ul class="test-steps">
                <li>直接访问带有 allFields 参数的 URL</li>
                <li>或在页面中进行全局搜索</li>
                <li>观察是否显示全局搜索标签</li>
                <li>标签应显示为绿色背景（区别于普通筛选器）</li>
            </ul>
            <div class="expected-result">
                <strong>预期结果：</strong> 显示 "全局搜索: 导管 ❌" 的绿色标签
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">4. 高级搜索标签测试</div>
            <div class="test-description">
                测试高级搜索条件是否能正确显示为标签。
            </div>
            <ul class="test-steps">
                <li>点击"Advanced Search"按钮</li>
                <li>添加一个或多个高级搜索条件</li>
                <li>点击搜索</li>
                <li>观察高级搜索条件是否显示为蓝色标签</li>
            </ul>
            <div class="expected-result">
                <strong>预期结果：</strong> 显示蓝色背景的高级搜索标签，格式包含操作符信息
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">5. 标签删除功能测试</div>
            <div class="test-description">
                <strong>🎯 核心功能测试</strong> - 验证点击 ❌ 能否正确删除单个搜索条件并重新搜索。
            </div>
            <ul class="test-steps">
                <li>确保有多个搜索条件（如：产品类别 + 全局搜索）</li>
                <li>点击其中一个标签的 ❌ 按钮</li>
                <li>观察该标签是否立即消失</li>
                <li>观察数据表格是否重新加载</li>
                <li>验证搜索结果是否反映了删除后的条件</li>
                <li>检查浏览器控制台是否有相关日志</li>
            </ul>
            <div class="expected-result">
                <strong>预期结果：</strong> 
                <br>• 点击的标签立即消失
                <br>• 其他标签保持不变
                <br>• 数据表格重新加载，显示删除条件后的结果
                <br>• 控制台显示删除和搜索的日志信息
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">6. 清空功能测试</div>
            <div class="test-description">
                测试"清空"按钮是否能正确清除所有搜索条件。
            </div>
            <ul class="test-steps">
                <li>确保有多个搜索条件标签</li>
                <li>点击"清空"按钮</li>
                <li>观察所有标签是否消失</li>
                <li>观察数据表格是否重新加载显示所有数据</li>
                <li>验证左侧筛选器是否重置</li>
            </ul>
            <div class="expected-result">
                <strong>预期结果：</strong> 所有标签消失，筛选器重置，显示完整数据集
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">7. 混合搜索条件测试</div>
            <div class="test-description">
                测试同时使用多种搜索方式的标签显示和删除功能。
            </div>
            <ul class="test-steps">
                <li>同时使用：全局搜索 + 左侧筛选器 + 高级搜索</li>
                <li>观察是否显示不同颜色的标签</li>
                <li>分别删除不同类型的标签</li>
                <li>验证每次删除后的搜索结果是否正确</li>
            </ul>
            <div class="expected-result">
                <strong>预期结果：</strong> 
                <br>• 绿色标签：全局搜索
                <br>• 灰色标签：普通筛选器
                <br>• 蓝色标签：高级搜索
                <br>• 每个标签都能独立删除并触发正确的搜索
            </div>
        </div>

        <div class="warning">
            <strong>⚠️ 调试提示：</strong>
            <br>• 打开浏览器开发者工具查看控制台日志
            <br>• 关注以 [FilterTag]、[SearchFilters]、[Search] 开头的日志
            <br>• 如果删除功能不工作，检查网络请求是否正常发送
            <br>• 验证 appliedFilters 和 advancedSearchConditions 状态是否正确更新
        </div>

        <div class="test-section">
            <div class="test-title">8. 性能和用户体验测试</div>
            <div class="test-description">
                验证搜索条件标签的性能和用户体验。
            </div>
            <ul class="test-steps">
                <li>测试大量搜索条件时的显示效果</li>
                <li>验证标签的响应式布局</li>
                <li>测试删除操作的响应速度</li>
                <li>验证标签的悬停效果</li>
                <li>测试移动端的显示效果</li>
            </ul>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 6px;">
            <h3>🎯 测试重点</h3>
            <p>本次修复的核心目标是实现 <strong>点击 ❌ 删除单个搜索条件并重新搜索</strong> 的功能。</p>
            <p>请特别关注第5项测试，确保删除功能完全正常工作。</p>
        </div>
    </div>
</body>
</html>
