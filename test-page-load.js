const puppeteer = require('puppeteer');

async function testPageLoad() {
  let browser;
  try {
    console.log('🚀 启动浏览器...');
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // 监听控制台错误
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ 控制台错误:', msg.text());
      }
    });
    
    // 监听页面错误
    page.on('pageerror', error => {
      console.log('❌ 页面错误:', error.message);
    });
    
    console.log('📄 访问页面: http://localhost:3000/data/list/us_pmn');
    
    // 设置较长的超时时间
    await page.goto('http://localhost:3000/data/list/us_pmn', { 
      waitUntil: 'networkidle0',
      timeout: 30000 
    });
    
    console.log('✅ 页面加载成功！');
    
    // 检查是否有错误信息
    const errorElements = await page.$$('[data-testid="error"], .error, [class*="error"]');
    if (errorElements.length > 0) {
      console.log('⚠️  发现错误元素:', errorElements.length);
    }
    
    // 检查页面标题
    const title = await page.title();
    console.log('📋 页面标题:', title);
    
    // 等待一下让页面完全加载
    await page.waitForTimeout(2000);
    
    console.log('🎉 测试完成，页面正常加载！');
    
  } catch (error) {
    console.log('❌ 测试失败:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
  return true;
}

// 如果直接运行此脚本
if (require.main === module) {
  testPageLoad().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = testPageLoad;
