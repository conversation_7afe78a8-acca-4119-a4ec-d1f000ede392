"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import SearchFilterTags from '@/components/SearchFilterTags';
import FilterStatsDisplay from '@/components/FilterStatsDisplay';
import { useSearchFilters } from '@/hooks/use-search-filters';
import type { FieldTypeMapping } from '@/types/search-filters';

// 模拟字段映射
const mockFieldMapping: FieldTypeMapping = {
  productName: {
    type: 'text',
    displayName: '产品名称'
  },
  category: {
    type: 'select',
    displayName: '管理类别',
    options: ['第一类', '第二类', '第三类']
  },
  company: {
    type: 'text',
    displayName: '公司名称'
  },
  registrationDate: {
    type: 'date_range',
    displayName: '注册日期',
    dateFormat: 'YYYY-MM-DD'
  },
  status: {
    type: 'multi_select',
    displayName: '状态',
    options: ['有效', '无效', '暂停', '注销']
  },
  isImported: {
    type: 'boolean',
    displayName: '是否进口'
  }
};

export default function TestFilterTagsPage() {
  const [simpleFilters, setSimpleFilters] = useState<Record<string, unknown>>({});
  
  const {
    filters,
    removeFilter,
    clearAllFilters,
    updateFromSimpleFilters,
    stats
  } = useSearchFilters({
    fieldMapping: mockFieldMapping,
    config: {
      maxTags: 10,
      autoSearch: false
    },
    onFiltersChange: (filters, event) => {
      console.log('Filters changed:', { filters, event });
    }
  });

  const handleAddFilter = (fieldName: string, value: unknown) => {
    const newFilters = { ...simpleFilters, [fieldName]: value };
    setSimpleFilters(newFilters);
    updateFromSimpleFilters(newFilters);
  };

  const handleRemoveSimpleFilter = (fieldName: string) => {
    const newFilters = { ...simpleFilters };
    delete newFilters[fieldName];
    setSimpleFilters(newFilters);
    updateFromSimpleFilters(newFilters);
  };

  const handleClearAll = () => {
    setSimpleFilters({});
    clearAllFilters();
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            搜索条件标签系统演示
          </h1>
          <p className="text-gray-600">
            模仿 https://bydrug.pharmcube.com/devicego/deviceCNImported 的搜索条件标签功能
          </p>
        </div>

        {/* 模拟筛选器面板 */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold mb-4">筛选器面板</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* 产品名称 */}
            <div>
              <label className="block text-sm font-medium mb-2">产品名称</label>
              <Input
                placeholder="输入产品名称"
                value={simpleFilters.productName as string || ''}
                onChange={(e) => handleAddFilter('productName', e.target.value)}
              />
            </div>

            {/* 管理类别 */}
            <div>
              <label className="block text-sm font-medium mb-2">管理类别</label>
              <Select
                value={simpleFilters.category as string || ''}
                onValueChange={(value) => handleAddFilter('category', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择类别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value="第一类">第一类</SelectItem>
                  <SelectItem value="第二类">第二类</SelectItem>
                  <SelectItem value="第三类">第三类</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 公司名称 */}
            <div>
              <label className="block text-sm font-medium mb-2">公司名称</label>
              <Input
                placeholder="输入公司名称"
                value={simpleFilters.company as string || ''}
                onChange={(e) => handleAddFilter('company', e.target.value)}
              />
            </div>

            {/* 状态 */}
            <div>
              <label className="block text-sm font-medium mb-2">状态</label>
              <Select
                value={simpleFilters.status as string || ''}
                onValueChange={(value) => handleAddFilter('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value="有效">有效</SelectItem>
                  <SelectItem value="无效">无效</SelectItem>
                  <SelectItem value="暂停">暂停</SelectItem>
                  <SelectItem value="注销">注销</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 是否进口 */}
            <div>
              <label className="block text-sm font-medium mb-2">是否进口</label>
              <Select
                value={simpleFilters.isImported as string || ''}
                onValueChange={(value) => handleAddFilter('isImported', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部</SelectItem>
                  <SelectItem value="true">是</SelectItem>
                  <SelectItem value="false">否</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-2 mt-4">
            <Button onClick={() => console.log('Search triggered')}>
              搜索
            </Button>
            <Button variant="outline" onClick={handleClearAll}>
              清空
            </Button>
          </div>
        </div>

        {/* 统计信息显示 */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold mb-4">统计信息显示</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-2">详细模式</h3>
              <FilterStatsDisplay
                appliedFiltersCount={stats.simpleFilters}
                advancedFiltersCount={stats.advancedFilters}
                pendingFiltersCount={0}
                totalRecords={1234}
                hasChanges={false}
                variant="detailed"
              />
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">紧凑模式</h3>
              <FilterStatsDisplay
                appliedFiltersCount={stats.simpleFilters}
                advancedFiltersCount={stats.advancedFilters}
                pendingFiltersCount={2}
                hasChanges={true}
                variant="compact"
              />
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">最小模式</h3>
              <FilterStatsDisplay
                appliedFiltersCount={stats.simpleFilters}
                advancedFiltersCount={stats.advancedFilters}
                pendingFiltersCount={0}
                variant="minimal"
              />
            </div>
          </div>
        </div>

        {/* 搜索条件标签展示 */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold mb-4">搜索条件标签</h2>
          <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 min-h-[100px]">
            {filters.length > 0 ? (
              <SearchFilterTags
                filters={filters}
                onRemoveFilter={removeFilter}
                onClearAll={handleClearAll}
                showStats={true}
                maxVisibleTags={10}
              />
            ) : (
              <div className="text-center text-gray-500 py-8">
                <p>暂无搜索条件</p>
                <p className="text-sm mt-1">在上方筛选器中添加条件后，这里会显示相应的标签</p>
              </div>
            )}
          </div>
        </div>

        {/* 调试信息 */}
        <div className="bg-gray-100 rounded-lg p-4">
          <h3 className="text-sm font-semibold mb-2">调试信息</h3>
          <div className="text-xs space-y-1">
            <div>当前筛选器: {JSON.stringify(simpleFilters, null, 2)}</div>
            <div>标签数量: {filters.length}</div>
            <div>统计信息: {JSON.stringify(stats, null, 2)}</div>
          </div>
        </div>
      </div>
    </div>
  );
}
