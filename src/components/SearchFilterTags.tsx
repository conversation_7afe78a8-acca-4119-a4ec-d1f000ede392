"use client";

import React from 'react';
import { X, Filter, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import type { SearchFilter, FilterTagConfig, FilterStats } from '@/types/search-filters';

interface SearchFilterTagsProps {
  filters: SearchFilter[];
  onRemoveFilter: (filterId: string) => void;
  onClearAll: () => void;
  config?: FilterTagConfig;
  className?: string;
  showStats?: boolean;
  maxVisibleTags?: number;
}

// 格式化显示值
const formatDisplayValue = (filter: SearchFilter, maxLength = 20): string => {
  const { value, type } = filter;
  
  if (Array.isArray(value)) {
    if (value.length === 0) return '';
    if (value.length === 1) return value[0].length > maxLength ? `${value[0].substring(0, maxLength)}...` : value[0];
    return `${value.length} items`;
  }
  
  if (typeof value === 'object' && value !== null) {
    const { from, to } = value as { from?: string; to?: string };
    if (from && to) return `${from} ~ ${to}`;
    if (from) return `>= ${from}`;
    if (to) return `<= ${to}`;
    return '';
  }
  
  const stringValue = String(value);
  return stringValue.length > maxLength ? `${stringValue.substring(0, maxLength)}...` : stringValue;
};

// 获取标签颜色
const getTagVariant = (filter: SearchFilter): "default" | "secondary" | "destructive" | "outline" => {
  switch (filter.source) {
    case 'advanced_search':
      return 'default';
    case 'filter_panel':
      return 'secondary';
    case 'global_search':
      return 'outline';
    default:
      return 'secondary';
  }
};

// 单个标签组件
const FilterTag: React.FC<{
  filter: SearchFilter;
  onRemove: (id: string) => void;
  config?: FilterTagConfig;
}> = ({ filter, onRemove, config = {} }) => {
  const displayValue = formatDisplayValue(filter, config.maxValueLength);

  if (!displayValue) return null;

  // 根据来源设置不同的样式
  const getTagStyle = () => {
    switch (filter.source) {
      case 'advanced_search':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'global_search':
        return 'bg-green-50 border-green-200 text-green-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  return (
    <div className={`inline-flex items-center gap-1 border rounded-md px-3 py-1.5 text-sm shadow-sm hover:shadow-md transition-all duration-200 ${getTagStyle()}`}>
      <div className="flex items-center gap-1">
        <span className="font-medium">{filter.displayName}:&nbsp;</span>
        <span className="font-normal">{displayValue}</span>
        {config.showOperator && filter.operator && (
          <span className="text-xs opacity-70">({filter.operator})</span>
        )}
      </div>
      <button
        className="ml-1 p-0.5 rounded hover:bg-red-100 hover:text-red-600 transition-colors duration-150"
        onClick={() => {
          console.log('[FilterTag] 点击删除按钮:', filter.id, filter.displayName);
          onRemove(filter.id);
        }}
        title={`删除 ${filter.displayName} 筛选条件`}
      >
        <X className="h-3 w-3" />
      </button>
    </div>
  );
};

// 统计信息组件
const FilterStats: React.FC<{ filters: SearchFilter[] }> = ({ filters }) => {
  const stats = filters.reduce((acc, filter) => {
    acc.total++;
    switch (filter.source) {
      case 'advanced_search':
        acc.advanced++;
        break;
      case 'filter_panel':
        acc.simple++;
        break;
      case 'global_search':
        acc.global++;
        break;
      default:
        acc.other++;
    }
    return acc;
  }, { total: 0, simple: 0, advanced: 0, global: 0, other: 0 });

  if (stats.total === 0) return null;

  return (
    <div className="text-xs text-gray-600 mb-2">
      Applied {stats.total} filter{stats.total > 1 ? 's' : ''}
      {stats.simple > 0 && ` (${stats.simple} basic)`}
      {stats.advanced > 0 && ` (${stats.advanced} advanced)`}
      {stats.global > 0 && ` (${stats.global} search)`}
    </div>
  );
};

export default function SearchFilterTags({
  filters,
  onRemoveFilter,
  onClearAll,
  config = {},
  className,
  showStats = true,
  maxVisibleTags = 10
}: SearchFilterTagsProps) {
  const visibleFilters = filters.slice(0, maxVisibleTags);
  const hiddenCount = Math.max(0, filters.length - maxVisibleTags);

  if (filters.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* 统计信息 */}
      {showStats && <FilterStats filters={filters} />}
      
      {/* 标签区域 */}
      <div className="flex flex-wrap items-center gap-2">
        {visibleFilters.map((filter) => (
          <FilterTag
            key={filter.id}
            filter={filter}
            onRemove={onRemoveFilter}
            config={config}
          />
        ))}
        
        {/* 隐藏标签数量提示 */}
        {hiddenCount > 0 && (
          <Badge variant="outline" className="text-xs">
            +{hiddenCount} more
          </Badge>
        )}
        
        {/* 清空按钮 - 模仿目标网站样式 */}
        <button
          className="inline-flex items-center px-3 py-1.5 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md transition-colors duration-150 border border-transparent hover:border-blue-200"
          onClick={() => {
            console.log('[FilterTags] 点击清空所有按钮');
            onClearAll();
          }}
          title="清空所有筛选条件"
        >
          清空
        </button>
      </div>
    </div>
  );
}
