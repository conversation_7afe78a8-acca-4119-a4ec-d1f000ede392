"use client";

import React from 'react';
import { Filter, Search, Database, BarChart3 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface FilterStatsDisplayProps {
  appliedFiltersCount: number;
  advancedFiltersCount: number;
  pendingFiltersCount: number;
  totalRecords?: number;
  hasChanges?: boolean;
  className?: string;
  variant?: 'compact' | 'detailed' | 'minimal';
}

export default function FilterStatsDisplay({
  appliedFiltersCount,
  advancedFiltersCount,
  pendingFiltersCount,
  totalRecords,
  hasChanges = false,
  className,
  variant = 'detailed'
}: FilterStatsDisplayProps) {
  const totalApplied = appliedFiltersCount + advancedFiltersCount;

  if (variant === 'minimal') {
    return (
      <div className={cn("flex items-center gap-2 text-xs text-gray-600", className)}>
        {totalApplied > 0 ? (
          <>
            <Filter className="h-3 w-3" />
            <span>{totalApplied} active</span>
          </>
        ) : (
          <span className="text-gray-400">No filters</span>
        )}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn("flex items-center gap-2 text-sm", className)}>
        <div className="flex items-center gap-1">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className={totalApplied > 0 ? "text-blue-600 font-medium" : "text-gray-500"}>
            {totalApplied > 0 ? `${totalApplied} applied` : 'No filters'}
          </span>
        </div>
        {hasChanges && (
          <Badge variant="outline" className="text-xs text-orange-600 border-orange-200">
            {pendingFiltersCount} pending
          </Badge>
        )}
      </div>
    );
  }

  // Detailed variant (default)
  return (
    <div className={cn("space-y-2", className)}>
      {/* Main status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className={totalApplied > 0 ? "text-blue-600 font-medium" : "text-gray-500"}>
              {totalApplied > 0 ? (
                <>
                  Applied: {appliedFiltersCount} filter{appliedFiltersCount !== 1 ? 's' : ''}
                  {advancedFiltersCount > 0 && (
                    <span className="text-blue-500"> + {advancedFiltersCount} advanced</span>
                  )}
                </>
              ) : (
                'No active filters'
              )}
            </span>
          </div>
          
          {totalRecords !== undefined && (
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Database className="h-3 w-3" />
              <span>{totalRecords.toLocaleString()} records</span>
            </div>
          )}
        </div>

        {/* Filter breakdown badges */}
        {totalApplied > 0 && (
          <div className="flex items-center gap-1">
            {appliedFiltersCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {appliedFiltersCount} basic
              </Badge>
            )}
            {advancedFiltersCount > 0 && (
              <Badge variant="default" className="text-xs">
                {advancedFiltersCount} advanced
              </Badge>
            )}
          </div>
        )}
      </div>

      {/* Pending changes indicator */}
      {hasChanges && (
        <div className="flex items-center gap-2 text-sm">
          <div className="flex items-center gap-1 text-orange-600">
            <BarChart3 className="h-3 w-3" />
            <span className="font-medium">
              {pendingFiltersCount} filter{pendingFiltersCount !== 1 ? 's' : ''} ready to apply
            </span>
          </div>
          <Badge variant="outline" className="text-xs text-orange-600 border-orange-200 bg-orange-50">
            Click Search to apply
          </Badge>
        </div>
      )}
    </div>
  );
}
