/**
 * 搜索条件标签系统的类型定义
 */

// 搜索条件的基础类型
export interface SearchFilter {
  id: string;
  fieldName: string;
  displayName: string;
  value: string | string[] | { from?: string; to?: string };
  operator?: string;
  type: 'simple' | 'advanced' | 'elasticsearch' | 'prisma';
  source: 'filter_panel' | 'advanced_search' | 'url_params' | 'global_search';
}

// 搜索条件标签的显示配置
export interface FilterTagConfig {
  showOperator?: boolean;
  showSource?: boolean;
  maxValueLength?: number;
  dateFormat?: string;
}

// 搜索条件统计信息
export interface FilterStats {
  totalFilters: number;
  simpleFilters: number;
  advancedFilters: number;
  elasticsearchFilters: number;
  prismaFilters: number;
}

// 搜索条件变更事件
export interface FilterChangeEvent {
  action: 'add' | 'remove' | 'clear' | 'update';
  filter?: SearchFilter;
  filters?: SearchFilter[];
}

// 搜索条件管理器的配置
export interface FilterManagerConfig {
  maxTags?: number;
  enableGrouping?: boolean;
  enableStats?: boolean;
  autoSearch?: boolean;
  debounceMs?: number;
}

// 字段类型映射，用于格式化显示值
export interface FieldTypeMapping {
  [fieldName: string]: {
    type: 'text' | 'select' | 'multi_select' | 'date' | 'date_range' | 'boolean' | 'number';
    displayName: string;
    options?: string[];
    dateFormat?: string;
  };
}

// 搜索条件的序列化格式（用于URL参数和API调用）
export interface SerializedFilters {
  simple: Record<string, unknown>;
  advanced: Array<{
    field: string;
    operator: string;
    value: unknown;
    logic?: 'AND' | 'OR' | 'NOT';
  }>;
  elasticsearch?: Record<string, unknown>;
  prisma?: Record<string, unknown>;
}
