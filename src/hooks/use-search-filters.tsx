"use client";

import { useState, useCallback, useRef, useEffect } from 'react';
import { debounce } from 'lodash';
import type { 
  SearchFilter, 
  FilterChangeEvent, 
  FilterManagerConfig, 
  FieldTypeMapping,
  SerializedFilters 
} from '@/types/search-filters';
import type { SearchCondition } from '@/components/AdvancedSearch';

interface UseSearchFiltersProps {
  fieldMapping: FieldTypeMapping;
  config?: FilterManagerConfig;
  onFiltersChange?: (filters: SearchFilter[], event: FilterChangeEvent) => void;
  onSearch?: (serializedFilters: SerializedFilters) => void;
}

export function useSearchFilters({
  fieldMapping,
  config = {},
  onFiltersChange,
  onSearch
}: UseSearchFiltersProps) {
  const [filters, setFilters] = useState<SearchFilter[]>([]);
  const filtersRef = useRef<SearchFilter[]>([]);
  
  // 配置默认值
  const {
    maxTags = 20,
    enableGrouping = true,
    enableStats = true,
    autoSearch = true,
    debounceMs = 300
  } = config;

  // 更新 ref
  useEffect(() => {
    filtersRef.current = filters;
  }, [filters]);

  // 生成唯一ID
  const generateId = useCallback(() => {
    return `filter_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  // 从简单筛选器创建搜索条件
  const createFilterFromSimple = useCallback((
    fieldName: string,
    value: unknown,
    source: SearchFilter['source'] = 'filter_panel'
  ): SearchFilter | null => {
    const fieldConfig = fieldMapping[fieldName];

    // 检查字段配置是否存在
    if (!fieldConfig) {
      console.log(`[SearchFilters] 未找到字段配置: ${fieldName}`);
      return null;
    }

    // 检查值是否有效
    if (!value ||
        (typeof value === 'string' && value.trim() === '') ||
        (Array.isArray(value) && value.length === 0)) {
      return null;
    }

    console.log(`[SearchFilters] 创建筛选条件: ${fieldName} = ${JSON.stringify(value)}`);

    return {
      id: generateId(),
      fieldName,
      displayName: fieldConfig.displayName,
      value,
      type: 'simple',
      source
    };
  }, [fieldMapping, generateId]);

  // 从高级搜索条件创建搜索条件
  const createFilterFromAdvanced = useCallback((condition: SearchCondition): SearchFilter | null => {
    const fieldConfig = fieldMapping[condition.field];
    if (!fieldConfig || !condition.value) {
      return null;
    }

    return {
      id: condition.id,
      fieldName: condition.field,
      displayName: fieldConfig.displayName,
      value: condition.value,
      operator: condition.operator,
      type: 'advanced',
      source: 'advanced_search'
    };
  }, [fieldMapping]);

  // 添加筛选条件
  const addFilter = useCallback((filter: SearchFilter) => {
    setFilters(prev => {
      // 检查是否已存在相同的筛选条件
      const existingIndex = prev.findIndex(f => 
        f.fieldName === filter.fieldName && 
        f.source === filter.source &&
        (filter.source !== 'advanced_search' || f.id === filter.id)
      );

      let newFilters: SearchFilter[];
      if (existingIndex >= 0) {
        // 更新现有筛选条件
        newFilters = [...prev];
        newFilters[existingIndex] = filter;
      } else {
        // 添加新筛选条件
        newFilters = [...prev, filter];
        
        // 检查最大标签数限制
        if (newFilters.length > maxTags) {
          newFilters = newFilters.slice(-maxTags);
        }
      }

      // 触发变更事件
      const event: FilterChangeEvent = {
        action: existingIndex >= 0 ? 'update' : 'add',
        filter,
        filters: newFilters
      };
      onFiltersChange?.(newFilters, event);

      return newFilters;
    });
  }, [maxTags, onFiltersChange]);

  // 移除筛选条件
  const removeFilter = useCallback((filterId: string) => {
    setFilters(prev => {
      const filterToRemove = prev.find(f => f.id === filterId);
      const newFilters = prev.filter(f => f.id !== filterId);
      
      if (filterToRemove) {
        const event: FilterChangeEvent = {
          action: 'remove',
          filter: filterToRemove,
          filters: newFilters
        };
        onFiltersChange?.(newFilters, event);
      }

      return newFilters;
    });
  }, [onFiltersChange]);

  // 清空所有筛选条件
  const clearAllFilters = useCallback(() => {
    setFilters(prev => {
      if (prev.length > 0) {
        const event: FilterChangeEvent = {
          action: 'clear',
          filters: []
        };
        onFiltersChange?.([], event);
      }
      return [];
    });
  }, [onFiltersChange]);

  // 从简单筛选器批量更新
  const updateFromSimpleFilters = useCallback((simpleFilters: Record<string, unknown>) => {
    const newFilters: SearchFilter[] = [];
    
    // 保留高级搜索条件
    const advancedFilters = filtersRef.current.filter(f => f.source === 'advanced_search');
    newFilters.push(...advancedFilters);

    // 添加简单筛选条件
    Object.entries(simpleFilters).forEach(([fieldName, value]) => {
      const filter = createFilterFromSimple(fieldName, value);
      if (filter) {
        newFilters.push(filter);
      }
    });

    setFilters(newFilters);
  }, [createFilterFromSimple]);

  // 从高级搜索条件批量更新
  const updateFromAdvancedSearch = useCallback((conditions: SearchCondition[]) => {
    const newFilters: SearchFilter[] = [];
    
    // 保留简单筛选条件
    const simpleFilters = filtersRef.current.filter(f => f.source !== 'advanced_search');
    newFilters.push(...simpleFilters);

    // 添加高级搜索条件
    conditions.forEach(condition => {
      const filter = createFilterFromAdvanced(condition);
      if (filter) {
        newFilters.push(filter);
      }
    });

    setFilters(newFilters);
  }, [createFilterFromAdvanced]);

  // 序列化筛选条件用于API调用
  const serializeFilters = useCallback((filtersToSerialize = filters): SerializedFilters => {
    const result: SerializedFilters = {
      simple: {},
      advanced: []
    };

    filtersToSerialize.forEach(filter => {
      if (filter.source === 'advanced_search') {
        result.advanced.push({
          field: filter.fieldName,
          operator: filter.operator || 'contains',
          value: filter.value
        });
      } else {
        result.simple[filter.fieldName] = filter.value;
      }
    });

    return result;
  }, [filters]);

  // 防抖搜索
  const debouncedSearch = useCallback(
    debounce((serializedFilters: SerializedFilters) => {
      onSearch?.(serializedFilters);
    }, debounceMs),
    [onSearch, debounceMs]
  );

  // 触发搜索
  const triggerSearch = useCallback(() => {
    if (autoSearch && onSearch) {
      const serialized = serializeFilters();
      debouncedSearch(serialized);
    }
  }, [autoSearch, onSearch, serializeFilters, debouncedSearch]);

  // 当筛选条件变化时自动触发搜索
  useEffect(() => {
    if (autoSearch) {
      triggerSearch();
    }
  }, [filters, triggerSearch, autoSearch]);

  return {
    filters,
    addFilter,
    removeFilter,
    clearAllFilters,
    updateFromSimpleFilters,
    updateFromAdvancedSearch,
    serializeFilters,
    triggerSearch,
    stats: {
      totalFilters: filters.length,
      simpleFilters: filters.filter(f => f.source !== 'advanced_search').length,
      advancedFilters: filters.filter(f => f.source === 'advanced_search').length
    }
  };
}
