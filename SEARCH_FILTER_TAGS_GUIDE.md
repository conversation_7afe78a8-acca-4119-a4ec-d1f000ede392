# 搜索条件标签系统实现指南

## 概述

本系统实现了类似 https://bydrug.pharmcube.com/devicego/deviceCNImported 的搜索条件标签功能，用户可以直观地看到当前应用的搜索条件，并能够快速删除单个条件或清空所有条件。

## 功能特性

### 1. 搜索条件标签显示
- **直观展示**：每个搜索条件以标签形式显示，格式为「字段名：值」
- **来源区分**：不同来源的搜索条件使用不同的样式区分
  - 基础筛选器：灰色边框
  - 高级搜索：蓝色背景
  - 全局搜索：轮廓样式
- **值格式化**：自动格式化不同类型的值（文本、日期范围、多选等）

### 2. 交互功能
- **单个删除**：点击标签右侧的 ✕ 按钮删除单个条件
- **批量清空**：点击「Clear All」按钮清空所有条件
- **自动搜索**：删除条件后自动触发重新搜索

### 3. 统计信息
- **智能统计**：显示已应用的筛选条件数量
- **分类统计**：区分基础筛选器和高级搜索条件
- **待提交提示**：显示待提交的筛选条件变更

## 核心组件

### 1. 类型定义 (`src/types/search-filters.ts`)

```typescript
interface SearchFilter {
  id: string;
  fieldName: string;
  displayName: string;
  value: string | string[] | { from?: string; to?: string };
  operator?: string;
  type: 'simple' | 'advanced' | 'elasticsearch' | 'prisma';
  source: 'filter_panel' | 'advanced_search' | 'url_params' | 'global_search';
}
```

### 2. 搜索条件标签组件 (`src/components/SearchFilterTags.tsx`)

主要功能：
- 渲染搜索条件标签
- 处理标签删除事件
- 显示统计信息
- 支持最大显示数量限制

### 3. 搜索条件管理器 Hook (`src/hooks/use-search-filters.tsx`)

主要功能：
- 管理搜索条件状态
- 处理不同来源的搜索条件
- 提供统一的操作接口
- 支持防抖搜索

### 4. 统计显示组件 (`src/components/FilterStatsDisplay.tsx`)

支持三种显示模式：
- **详细模式**：显示完整的统计信息和待提交状态
- **紧凑模式**：适合移动端的简化显示
- **最小模式**：只显示基本的筛选器数量

## 集成方式

### 1. 在现有页面中集成

```typescript
// 1. 导入必要的组件和 Hook
import SearchFilterTags from '@/components/SearchFilterTags';
import { useSearchFilters } from '@/hooks/use-search-filters';
import FilterStatsDisplay from '@/components/FilterStatsDisplay';

// 2. 创建字段映射
const fieldMapping: FieldTypeMapping = {
  productName: {
    type: 'text',
    displayName: '产品名称'
  },
  category: {
    type: 'select',
    displayName: '管理类别',
    options: ['第一类', '第二类', '第三类']
  }
  // ... 更多字段
};

// 3. 初始化搜索条件管理器
const {
  filters,
  removeFilter,
  clearAllFilters,
  updateFromSimpleFilters,
  updateFromAdvancedSearch
} = useSearchFilters({
  fieldMapping,
  config: { maxTags: 15, autoSearch: false }
});

// 4. 在 JSX 中使用组件
<SearchFilterTags
  filters={filters}
  onRemoveFilter={handleRemoveFilterTag}
  onClearAll={handleClearAllFilterTags}
  showStats={true}
  maxVisibleTags={8}
/>
```

### 2. 处理筛选条件变更

```typescript
// 当简单筛选器变更时
const handleFiltersChange = (newFilters: Record<string, unknown>) => {
  updateFromSimpleFilters(newFilters);
  // 触发搜索
  performSearch(newFilters);
};

// 当高级搜索条件变更时
const handleAdvancedSearch = (conditions: SearchCondition[]) => {
  updateFromAdvancedSearch(conditions);
  // 触发搜索
  performAdvancedSearch(conditions);
};

// 处理标签删除
const handleRemoveFilterTag = async (filterId: string) => {
  const filterToRemove = filters.find(f => f.id === filterId);
  if (!filterToRemove) return;

  if (filterToRemove.source === 'advanced_search') {
    // 删除高级搜索条件
    const newConditions = advancedSearchConditions.filter(c => c.id !== filterId);
    setAdvancedSearchConditions(newConditions);
    updateFromAdvancedSearch(newConditions);
  } else {
    // 删除简单筛选条件
    const newFilters = { ...appliedFilters };
    delete newFilters[filterToRemove.fieldName];
    setAppliedFilters(newFilters);
    updateFromSimpleFilters(newFilters);
  }

  removeFilter(filterId);
  // 触发重新搜索
  performSearch();
};
```

## 样式定制

### 1. 标签样式

标签使用 Tailwind CSS 类进行样式化，可以通过修改 `SearchFilterTags.tsx` 中的类名来定制外观：

```typescript
// 标签容器
<div className="inline-flex items-center gap-1 bg-white border border-gray-200 rounded-md px-2 py-1 text-sm shadow-sm hover:shadow-md transition-shadow">

// 字段名
<span className="font-medium text-gray-700">{filter.displayName}:</span>

// 值
<span className="text-gray-900">{displayValue}</span>

// 删除按钮
<Button className="h-4 w-4 p-0 hover:bg-red-50 hover:text-red-600">
```

### 2. 统计信息样式

可以通过 `variant` 属性选择不同的显示模式：

```typescript
<FilterStatsDisplay
  variant="detailed" // 'detailed' | 'compact' | 'minimal'
  appliedFiltersCount={appliedCount}
  advancedFiltersCount={advancedCount}
  // ... 其他属性
/>
```

## 配置选项

### 1. 搜索条件管理器配置

```typescript
const config: FilterManagerConfig = {
  maxTags: 15,           // 最大标签数量
  enableGrouping: true,  // 启用分组
  enableStats: true,     // 启用统计
  autoSearch: false,     // 自动搜索
  debounceMs: 300       // 防抖延迟
};
```

### 2. 标签显示配置

```typescript
const tagConfig: FilterTagConfig = {
  showOperator: true,    // 显示操作符
  showSource: false,     // 显示来源
  maxValueLength: 20,    // 最大值长度
  dateFormat: 'YYYY-MM-DD' // 日期格式
};
```

## 最佳实践

### 1. 性能优化
- 使用 `useMemo` 缓存字段映射
- 使用防抖避免频繁的搜索请求
- 限制最大标签数量避免界面过于拥挤

### 2. 用户体验
- 提供清晰的视觉反馈
- 支持键盘操作
- 在移动端使用紧凑模式

### 3. 数据一致性
- 确保标签状态与实际筛选条件同步
- 处理异步操作时的状态管理
- 提供错误处理和恢复机制

## 演示页面

访问 `/test-filter-tags` 查看完整的功能演示，包括：
- 不同类型筛选器的标签显示
- 标签删除和清空功能
- 统计信息的不同显示模式
- 实时的状态更新

## 扩展功能

### 1. 标签分组
可以根据筛选条件来源或类型对标签进行分组显示。

### 2. 标签排序
支持按照添加时间、字段名称或重要性对标签进行排序。

### 3. 标签持久化
将标签状态保存到 URL 参数或本地存储中，支持页面刷新后恢复。

### 4. 批量操作
支持选择多个标签进行批量删除或编辑。

## 故障排除

### 1. 标签不显示
- 检查字段映射是否正确配置
- 确认筛选条件值不为空
- 验证组件导入路径

### 2. 删除功能不工作
- 检查事件处理函数是否正确绑定
- 确认筛选条件 ID 是否唯一
- 验证状态更新逻辑

### 3. 样式问题
- 确认 Tailwind CSS 类名正确
- 检查 CSS 冲突
- 验证响应式设计

## 总结

本搜索条件标签系统提供了一个完整的解决方案，实现了直观的搜索条件展示和管理功能。通过模块化的设计，可以轻松集成到现有项目中，并支持灵活的定制和扩展。
